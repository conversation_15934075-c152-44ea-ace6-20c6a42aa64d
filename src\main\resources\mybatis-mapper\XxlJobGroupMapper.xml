<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" 
	"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.xxl.job.admin.dao.XxlJobGroupDao">
	
	<resultMap id="XxlJobGroup" type="com.xxl.job.admin.core.model.XxlJobGroup" >
		<result column="id" property="id" />
	    <result column="app_name" property="appName" />
	    <result column="title" property="title" />
	    <result column="order" property="order" />
		<result column="address_type" property="addressType" />
		<result column="address_list" property="addressList" />
		<result column="server_id" property="serverId" />
	</resultMap>

	<sql id="Base_Column_List">
		t.id,
		t.app_name,
		t.title,
		t.`order`,
		t.address_type,
		t.address_list,
		t.server_id
	</sql>

	<select id="findAll" resultMap="XxlJobGroup">
		SELECT <include refid="Base_Column_List" />
		FROM xxl_job_group AS t
		ORDER BY t.order ASC
	</select>

	<select id="findByAddressType" parameterType="java.lang.Integer" resultMap="XxlJobGroup">
		SELECT <include refid="Base_Column_List" />
		FROM xxl_job_group AS t
		WHERE t.address_type = #{addressType}
		ORDER BY t.order ASC
	</select>

	<insert id="save" parameterType="com.xxl.job.admin.core.model.XxlJobGroup" useGeneratedKeys="true" keyProperty="id" >
		INSERT INTO xxl_job_group ( `app_name`, `title`, `order`, `address_type`, `address_list`)
		values ( #{appName}, #{title}, #{order}, #{addressType}, #{addressList});
	</insert>

	<insert id="saveFromRpc" parameterType="com.xxl.job.admin.core.model.XxlJobGroup" useGeneratedKeys="true" keyProperty="id" >
		INSERT INTO xxl_job_group ( `app_name`, `title`, `order`, `address_type`, `address_list`, `server_id`)
		values ( #{appName}, #{title}, #{order}, #{addressType}, #{addressList}, #{serverId});
	</insert>

	<update id="update" parameterType="com.xxl.job.admin.core.model.XxlJobGroup" >
		UPDATE xxl_job_group
		SET `app_name` = #{appName},
			`title` = #{title},
			`order` = #{order},
			`address_type` = #{addressType},
			`address_list` = #{addressList}
		WHERE id = #{id}
	</update>

	<delete id="remove" parameterType="java.lang.Integer" >
		DELETE FROM xxl_job_group
		WHERE id = #{id}
	</delete>

	<select id="load" parameterType="java.lang.Integer" resultMap="XxlJobGroup">
		SELECT <include refid="Base_Column_List" />
		FROM xxl_job_group AS t
		WHERE t.id = #{id}
	</select>

	<select id="findByServerId" parameterType="java.lang.String" resultMap="XxlJobGroup">
		SELECT <include refid="Base_Column_List" />
		FROM xxl_job_group AS t
		WHERE t.server_id = #{serverId}
	</select>

</mapper>