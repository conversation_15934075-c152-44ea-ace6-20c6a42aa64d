package com.xxl.rpc.remoting.net.common;

import com.xxl.rpc.remoting.invoker.XxlRpcInvokerFactory;
import com.xxl.rpc.remoting.invoker.reference.XxlRpcReferenceBean;
import com.xxl.rpc.remoting.net.params.BaseCallback;
import com.xxl.rpc.remoting.net.params.XxlRpcRequest;
import com.xxl.rpc.serialize.Serializer;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.Executors;
import java.util.concurrent.ScheduledExecutorService;
import java.util.concurrent.TimeUnit;

/**
 * <AUTHOR> 2018-10-19
 */
public abstract class ConnectClient {
    protected static transient Logger logger = LoggerFactory.getLogger(ConnectClient.class);


    // ---------------------- iface ----------------------

    public abstract void init(String address, final Serializer serializer, final XxlRpcInvokerFactory xxlRpcInvokerFactory) throws Exception;

    public abstract void close();

    public abstract boolean isValidate();

    public abstract void send(XxlRpcRequest xxlRpcRequest) throws Exception ;


    // ---------------------- client pool map ----------------------

    /**
     * async send - 完全并发安全的发送方法
     * 使用与清理线程相同的锁机制，确保发送过程中连接不会被清理
     */
    public static void asyncSend(XxlRpcRequest xxlRpcRequest, String address,
                                 Class<? extends ConnectClient> connectClientImpl,
                                 final XxlRpcReferenceBean xxlRpcReferenceBean) throws Exception {

        // 获取地址对应的锁对象
        Object clientLock = connectClientLockMap.get(address);
        if (clientLock == null) {
            connectClientLockMap.putIfAbsent(address, new Object());
            clientLock = connectClientLockMap.get(address);
        }

        // 关键改进：整个获取-验证-发送过程在锁保护下，确保原子性
        synchronized (clientLock) {
            try {
                // 在锁保护下获取连接
                ConnectClient clientPool = ConnectClient.getPool(address, connectClientImpl, xxlRpcReferenceBean);

                // 在锁保护下进行最终验证
                if (clientPool != null && clientPool.isValidate() && !isConnectionExpired(address)) {
                    // 安全发送：此时清理线程无法同时执行
                    clientPool.send(xxlRpcRequest);
                    logger.debug(">>>>>>>>>>> xxl-rpc, {} send success to address: {}", getServerInfo(), address);
                } else {
                    // 连接无效，抛出异常
                    throw new Exception("Connection not available or expired for address: " + address);
                }
            } catch (Exception e) {
                logger.warn(">>>>>>>>>>> xxl-rpc, send failed to address: " + address, e);
                throw e;
            }
        }
    }

    private static volatile ConcurrentHashMap<String, ConnectClient> connectClientMap;        // (static) alread addStopCallBack
    private static volatile ConcurrentHashMap<String, Object> connectClientLockMap = new ConcurrentHashMap<>();

    // 连接生命周期管理 - 解决TCP连接时序问题
    private static volatile ConcurrentHashMap<String, Long> connectionCreateTimeMap = new ConcurrentHashMap<>();
    private static final long MAX_CONNECTION_LIFETIME = 7 * 60 * 1000; // 7分钟最大生命周期（确保在客户端10分钟前清理）
    private static ScheduledExecutorService connectionCleanupExecutor;
    private static ConnectClient getPool(String address, Class<? extends ConnectClient> connectClientImpl,
                                         final XxlRpcReferenceBean xxlRpcReferenceBean) throws Exception {

        // init base compont, avoid repeat init
        if (connectClientMap == null) {
            synchronized (ConnectClient.class) {
                if (connectClientMap == null) {
                    // init
                    connectClientMap = new ConcurrentHashMap<String, ConnectClient>();
                    connectionCreateTimeMap = new ConcurrentHashMap<String, Long>();

                    // 启动连接清理定时任务 - 核心：解决TCP连接时序问题
                    startConnectionCleanupTask();

                    // stop callback
                    xxlRpcReferenceBean.getInvokerFactory().addStopCallBack(new BaseCallback() {
                        @Override
                        public void run() throws Exception {
                            // 停止定时清理任务
                            if (connectionCleanupExecutor != null && !connectionCleanupExecutor.isShutdown()) {
                                connectionCleanupExecutor.shutdown();
                                try {
                                    if (!connectionCleanupExecutor.awaitTermination(5, TimeUnit.SECONDS)) {
                                        connectionCleanupExecutor.shutdownNow();
                                    }
                                } catch (InterruptedException e) {
                                    connectionCleanupExecutor.shutdownNow();
                                }
                            }

                            // 清理连接
                            if (connectClientMap.size() > 0) {
                                for (String key: connectClientMap.keySet()) {
                                    ConnectClient clientPool = connectClientMap.get(key);
                                    clientPool.close();
                                }
                                connectClientMap.clear();
                                connectionCreateTimeMap.clear();
                            }
                        }
                    });
                }
            }
        }

        // get-valid client - 增加连接生命周期检查
        ConnectClient connectClient = connectClientMap.get(address);
        if (connectClient != null && connectClient.isValidate() && !isConnectionExpired(address)) {
            return connectClient;
        }

        // 获取地址对应的锁对象（与asyncSend使用相同的锁获取逻辑）
        Object clientLock = connectClientLockMap.get(address);
        if (clientLock == null) {
            connectClientLockMap.putIfAbsent(address, new Object());
            clientLock = connectClientLockMap.get(address);
        }

        // remove-create new client - 使用锁确保并发安全
        synchronized (clientLock) {

            // 在锁内再次检查连接状态，避免并发清理导致的问题
            connectClient = connectClientMap.get(address);
            if (connectClient != null && connectClient.isValidate() && !isConnectionExpired(address)) {
                logger.debug(">>>>>>>>>>> xxl-rpc, reuse existing valid connection: {}", address);
                return connectClient;
            }

            // remove old - 安全地清理过期或无效连接
            if (connectClient != null) {
                try {
                    connectClient.close();
                } catch (Exception e) {
                    logger.warn(">>>>>>>>>>> xxl-rpc, error closing old connection: " + address, e);
                }
                connectClientMap.remove(address);
                connectionCreateTimeMap.remove(address);
                logger.debug(">>>>>>>>>>> xxl-rpc, removed expired/invalid connection: {}", address);
            }

            // set pool - 创建新连接并记录创建时间
            ConnectClient connectClient_new = connectClientImpl.newInstance();
            try {
                connectClient_new.init(address, xxlRpcReferenceBean.getSerializer(), xxlRpcReferenceBean.getInvokerFactory());

                // 原子性地添加到连接池和时间记录
                connectClientMap.put(address, connectClient_new);
                connectionCreateTimeMap.put(address, System.currentTimeMillis());

                logger.info(">>>>>>>>>>> xxl-rpc, {} created new connection: {}, lifetime: {} minutes",
                           getServerInfo(), address, MAX_CONNECTION_LIFETIME / (60 * 1000));
            } catch (Exception e) {
                // 创建失败时清理资源
                try {
                    connectClient_new.close();
                } catch (Exception closeEx) {
                    logger.warn(">>>>>>>>>>> xxl-rpc, error closing failed connection", closeEx);
                }
                throw e;
            }

            return connectClient_new;
        }

    }

    // ---------------------- 连接生命周期管理 - 解决TCP连接时序问题 ----------------------

    /**
     * 启动连接清理定时任务
     * 核心：服务端主动管理连接生命周期，在客户端断开前主动断开
     */
    private static void startConnectionCleanupTask() {
        connectionCleanupExecutor = Executors.newScheduledThreadPool(1, r -> {
            Thread t = new Thread(r, "xxl-rpc-connection-cleanup");
            t.setDaemon(true);
            return t;
        });

        // 每2分钟检查一次连接，清理过期连接
        connectionCleanupExecutor.scheduleWithFixedDelay(() -> {
            try {
                cleanupExpiredConnections();
            } catch (Exception e) {
                logger.error(">>>>>>>>>>> xxl-rpc, connection cleanup error", e);
            }
        }, 2, 2, TimeUnit.MINUTES);

        logger.info(">>>>>>>>>>> xxl-rpc, {} connection cleanup task started, max lifetime: {} minutes",
                   getServerInfo(), MAX_CONNECTION_LIFETIME / (60 * 1000));
    }

    /**
     * 检查连接是否过期
     * @param address 连接地址
     * @return true=过期，false=未过期
     */
    private static boolean isConnectionExpired(String address) {
        Long createTime = connectionCreateTimeMap.get(address);
        if (createTime == null) {
            return true; // 没有创建时间记录，认为过期
        }

        long currentTime = System.currentTimeMillis();
        boolean expired = (currentTime - createTime) > MAX_CONNECTION_LIFETIME;

        if (expired) {
            logger.debug(">>>>>>>>>>> xxl-rpc, connection expired: {}, age: {} ms",
                        address, currentTime - createTime);
        }

        return expired;
    }



    /**
     * 清理过期连接
     * 核心逻辑：主动清理超过7分钟的连接，避免客户端10分钟断开时的时序问题
     * 并发安全：使用与getPool相同的锁机制，避免清理时的并发冲突
     * 性能优化：只扫描实际存在连接的地址，避免全量扫描
     */
    private static void cleanupExpiredConnections() {
        if (connectClientMap == null || connectClientMap.isEmpty()) {
            return;
        }

        long currentTime = System.currentTimeMillis();
        List<String> expiredAddresses = new ArrayList<>();

        // 性能优化：只检查实际存在连接的地址，避免扫描已清理的地址
        for (String address : connectClientMap.keySet()) {
            Long createTime = connectionCreateTimeMap.get(address);

            if (createTime != null && (currentTime - createTime) > MAX_CONNECTION_LIFETIME) {
                expiredAddresses.add(address);
            }
        }

        // 安全地清理过期连接 - 使用相同的锁机制避免并发问题
        int cleanedCount = 0;
        for (String address : expiredAddresses) {
            // 获取与getPool方法相同的锁
            Object clientLock = connectClientLockMap.get(address);
            if (clientLock != null) {
                synchronized (clientLock) {
                    // 在锁保护下再次检查连接状态，确保并发安全
                    ConnectClient client = connectClientMap.get(address);
                    if (client != null && isConnectionExpired(address)) {
                        try {
                            // 原子性地关闭连接并从Map中移除
                            client.close();
                            connectClientMap.remove(address);
                            connectionCreateTimeMap.remove(address);

                            // 修复内存泄漏：清理不再使用的锁对象
                            connectClientLockMap.remove(address);

                            cleanedCount++;
                            logger.debug(">>>>>>>>>>> xxl-rpc, safely cleaned expired connection and lock: {}", address);
                        } catch (Exception e) {
                            logger.warn(">>>>>>>>>>> xxl-rpc, error cleaning connection: " + address, e);
                        }
                    }
                }
            } else {
                // 如果没有锁对象，说明连接可能已经被清理，直接从时间Map中移除
                connectionCreateTimeMap.remove(address);
                logger.debug(">>>>>>>>>>> xxl-rpc, removed orphaned connection time record: {}", address);
            }
        }

        if (cleanedCount > 0) {
            logger.info(">>>>>>>>>>> xxl-rpc, {} safely cleaned {} expired connections, remaining: {}",
                       getServerInfo(), cleanedCount, connectClientMap.size());
        }
    }

    /**
     * 获取连接池状态信息（用于监控）
     * 包含服务器IP信息，便于多实例环境下的监控对比
     * 返回格式化的状态信息，便于在JSON中显示
     */
    public static String getConnectionPoolStatus() {
        if (connectClientMap == null) {
            return getServerInfo() + " - Connection pool not initialized";
        }

        int totalConnections = connectClientMap.size();
        int validConnections = 0;
        int expiredConnections = 0;

        StringBuilder result = new StringBuilder();

        // 服务器信息和汇总
        result.append(String.format("%s - ConnectClient Pool: total=%d, valid=%d, expired=%d",
                                   getServerInfo(), totalConnections, validConnections, expiredConnections));

        if (totalConnections > 0) {
            result.append("\\n=== Connection Details ===");

            for (Map.Entry<String, ConnectClient> entry : connectClientMap.entrySet()) {
                String address = entry.getKey();
                ConnectClient client = entry.getValue();
                Long createTime = connectionCreateTimeMap.get(address);

                String status;
                if (client.isValidate()) {
                    if (isConnectionExpired(address)) {
                        status = "EXPIRED";
                        expiredConnections++;
                    } else {
                        status = "VALID";
                        validConnections++;
                    }
                } else {
                    status = "INVALID";
                }

                long ageMinutes = createTime != null ?
                    (System.currentTimeMillis() - createTime) / (60 * 1000) : -1;

                String createTimeStr = createTime != null ?
                    new java.text.SimpleDateFormat("HH:mm:ss").format(new java.util.Date(createTime)) : "Unknown";

                result.append(String.format("\\n• %s -> %s (age: %d min, created: %s)",
                    address, status, ageMinutes, createTimeStr));
            }

            // 重新计算正确的统计数据
            result.setLength(0);
            result.append(String.format("%s - ConnectClient Pool: total=%d, valid=%d, expired=%d",
                                       getServerInfo(), totalConnections, validConnections, expiredConnections));
            result.append("\\n=== Connection Details ===");

            validConnections = 0;
            expiredConnections = 0;

            for (Map.Entry<String, ConnectClient> entry : connectClientMap.entrySet()) {
                String address = entry.getKey();
                ConnectClient client = entry.getValue();
                Long createTime = connectionCreateTimeMap.get(address);

                String status;
                if (client.isValidate()) {
                    if (isConnectionExpired(address)) {
                        status = "EXPIRED";
                        expiredConnections++;
                    } else {
                        status = "VALID";
                        validConnections++;
                    }
                } else {
                    status = "INVALID";
                }

                long ageMinutes = createTime != null ?
                    (System.currentTimeMillis() - createTime) / (60 * 1000) : -1;

                String createTimeStr = createTime != null ?
                    new java.text.SimpleDateFormat("HH:mm:ss").format(new java.util.Date(createTime)) : "Unknown";

                result.append(String.format("\\n• %s -> %s (age: %d min, created: %s)",
                    address, status, ageMinutes, createTimeStr));
            }
        }

        return result.toString();
    }

    /**
     * 获取服务器信息（IP + 进程信息）
     */
    private static String getServerInfo() {
        try {
            String hostName = java.net.InetAddress.getLocalHost().getHostName();
            String hostAddress = java.net.InetAddress.getLocalHost().getHostAddress();
            String processInfo = java.lang.management.ManagementFactory.getRuntimeMXBean().getName();

            return String.format("Server[%s/%s, PID:%s]",
                               hostName, hostAddress, processInfo.split("@")[0]);
        } catch (Exception e) {
            return "Server[Unknown]";
        }
    }

}
