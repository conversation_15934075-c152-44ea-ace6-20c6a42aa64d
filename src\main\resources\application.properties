## \u4F18\u5316\u914D\u7F6E\uFF0C\u4E0D\u4F7F\u7528\u539F\u6765\u8BBE\u7F6E\u7684
spring.datasource.hikari.pool-name=eos-xxl-job-test
spring.datasource.hikari.minimum-idle=30
spring.datasource.hikari.maximum-pool-size=100
spring.datasource.hikari.idle-timeout=150000
spring.datasource.hikari.max-lifetime=300000
spring.datasource.hikari.connection-timeout=30000
## \u6709\u6548\u6027\u68C0\u67E5
spring.datasource.hikari.connection-test-query=SELECT 1
spring.datasource.hikari.validation-timeout=1500


spring.application.name=eos-xxl-job-test
spring.application.password=abc123!

spring.mvc.static-path-pattern=/static/**
spring.resources.static-locations=classpath:/static/

spring.freemarker.templateLoaderPath=classpath:/templates/
spring.freemarker.suffix=.ftl
spring.freemarker.charset=UTF-8
spring.freemarker.request-context-attribute=request
spring.freemarker.settings.number_format=0.##########
mybatis.mapper-locations=classpath:/mybatis-mapper/*Mapper.xml
spring.datasource.url=***************************************************************************
spring.datasource.username=root
spring.datasource.password=abc123!
spring.datasource.driver-class-name=com.mysql.jdbc.Driver

spring.mail.host=hzsmtp.h3c.com
#spring.mail.username=<EMAIL>
#spring.mail.password=abc123!A

spring.mail.username=<EMAIL>
spring.mail.password=5Yw&c4-C

#spring.mail.port=25

spring.mail.properties.mail.smtp.auth=true
spring.mail.properties.mail.smtp.starttls.enable=false
spring.mail.properties.mail.smtp.starttls.required=false
spring.mail.properties.mail.smtp.socketFactory.class=javax.net.ssl.SSLSocketFactory
xxl.job.accessToken=
xxl.job.i18n=

eos.project.name=eos-project
eos.project.url=http://api.eos-ts.h3c.com/project/v1.0

spring.cloud.config.allow-override=true
spring.cloud.config.override-none=true



###################################################################
####################security SDK \u914D\u7F6E\u9879 ############################
##\u6587\u6863\u5730\u5740\uFF1Ahttp://dev.eos.h3c.com/devResources/sdk/security.html ##
###################################################################
#h3c.it.security.paas.service-id=ai-vioce-text-interconversion
#h3c.it.security.enabled=true
#h3c.it.security.authorization.enabled=false
#h3c.it.security.sso.secret-key=ABCD1235
#h3c.it.security.sso.sso-local-site-id=test
#h3c.it.security.unsecured[0].method=get
#h3c.it.security.unsecured[0].uri-patterns[0]=/nAuth
#h3c.it.security.unsecured[0].uri-patterns[1]=/joblog/nLogDetailPage
#h3c.it.security.unsecured[0].uri-patterns[2]=/joblog/nLogKill
#h3c.it.security.unsecured[0].uri-patterns[3]=/joblog/nClearLog
#h3c.it.security.unsecured[0].uri-patterns[4]=/joblog/nGetJobsByGroup
#
#
#h3c.it.security.unsecured[1].method=post
#h3c.it.security.unsecured[1].uri-patterns[0]=/jobinfo/nPageList
#h3c.it.security.unsecured[1].uri-patterns[1]=/jobinfo/nAdd
#h3c.it.security.unsecured[1].uri-patterns[2]=/jobinfo/nUpdate
#h3c.it.security.unsecured[1].uri-patterns[3]=/jobinfo/nRemove
#h3c.it.security.unsecured[1].uri-patterns[4]=/jobinfo/nStop
#h3c.it.security.unsecured[1].uri-patterns[5]=/jobinfo/nStart
#h3c.it.security.unsecured[1].uri-patterns[6]=/jobinfo/nTrigger
#h3c.it.security.unsecured[1].uri-patterns[7]=/jobinfo/nNextTriggerTime
#h3c.it.security.unsecured[1].uri-patterns[8]=/jobinfo/infos
#
#h3c.it.security.unsecured[1].uri-patterns[9]=/joblog/jobLogInfos
#h3c.it.security.unsecured[1].uri-patterns[10]=/joblog/nPageList
#h3c.it.security.unsecured[1].uri-patterns[11]=/joblog/nLogDetailCat
#h3c.it.security.unsecured[1].uri-patterns[12]=/jobgroup/nLoadById



#redis\u914D\u7F6E
spring.redis.host=***********
spring.redis.port=6379
spring.redis.password=root23456
spring.redis.database=1
spring.redis.timeout=3000

logging.level.com.xxl=DEBUG

feiyun.ip.subnet=**********/24,**********/24
pod.ip.subnet=***********/18
eos.ip.subnet=***********/16
subnet.enable=false

# ip\u767D\u540D\u5355
ip.whitelist=*************/30
# ip\u767D\u540D\u5355\u5F00\u5173
ip.whitelist.enable=false