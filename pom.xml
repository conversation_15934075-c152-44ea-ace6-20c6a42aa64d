<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <parent>
        <groupId>org.springframework.boot</groupId>
        <artifactId>spring-boot-starter-parent</artifactId>
        <version>2.1.5.RELEASE</version>
        <relativePath/> <!-- lookup parent from repository -->
    </parent>
    <groupId>com.h3c</groupId>
    <artifactId>xxl-job-admin</artifactId>
    <packaging>jar</packaging>
    <version>1.0.1.RELEASE</version>

    <properties>
        <java.version>1.8</java.version>
        <xxl-rpc.version>1.4.1</xxl-rpc.version>
        <commons-exec.version>1.3</commons-exec.version>
        <groovy.version>2.5.7</groovy.version>

        <!-- RPC -->
        <slf4j-api.version>1.7.26</slf4j-api.version>
        <netty.version>4.1.36.Final</netty.version>
        <mina.version>2.1.2</mina.version>
        <jetty-server.version>9.2.28.v20190418</jetty-server.version>
        <hessian.version>4.0.60</hessian.version>
        <protostuff.version>1.6.0</protostuff.version>
        <objenesis.version>2.6</objenesis.version>
        <kryo.version>4.0.2</kryo.version>
        <xxl-registry.version>1.0.2</xxl-registry.version>
        <zookeeper.version>3.4.14</zookeeper.version>
    </properties>
    <dependencies>
        <dependency>
            <groupId>com.eos</groupId>
            <artifactId>eos-web-spring-boot-starter</artifactId>
            <version>1.1.0.RELEASE</version>
        </dependency>

        <dependency>
            <groupId>org.springframework.cloud</groupId>
            <artifactId>spring-cloud-starter-openfeign</artifactId>
            <version>2.1.2.RELEASE</version>
        </dependency>

        <dependency>
            <groupId>org.apache.commons</groupId>
            <artifactId>commons-lang3</artifactId>
            <version>3.8.1</version>
        </dependency>
        <dependency>
            <groupId>com.google.code.gson</groupId>
            <artifactId>gson</artifactId>
            <version>2.8.2</version>
        </dependency>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-amqp</artifactId>
            <version>2.2.4.RELEASE</version>
        </dependency>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-web</artifactId>
        </dependency>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-test</artifactId>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-freemarker</artifactId>
        </dependency>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-mail</artifactId>
        </dependency>
        <dependency>
            <groupId>org.mybatis.spring.boot</groupId>
            <artifactId>mybatis-spring-boot-starter</artifactId>
            <version>1.3.2</version>
        </dependency>
        <!-- mysql -->
        <dependency>
            <groupId>mysql</groupId>
            <artifactId>mysql-connector-java</artifactId>
        </dependency>

        <!-- xxl-job-core -->
        <!-- <dependency> -->
        <!--     <groupId>com.xuxueli</groupId> -->
        <!--     <artifactId>xxl-job-core</artifactId> -->
        <!--     <version>2.1.0</version> -->
        <!-- </dependency> -->

        <!-- xxl-rpc-core -->
        <!-- <dependency> -->
        <!--     <groupId>com.xuxueli</groupId> -->
        <!--     <artifactId>xxl-rpc-core</artifactId> -->
        <!--     <version>${xxl-rpc.version}</version> -->
        <!-- </dependency> -->

        <!-- ********************** base ********************** -->

        <!-- slf4j -->
        <dependency>
            <groupId>org.slf4j</groupId>
            <artifactId>slf4j-api</artifactId>
            <version>${slf4j-api.version}</version>
        </dependency>
        <dependency>
            <groupId>org.slf4j</groupId>
            <artifactId>slf4j-log4j12</artifactId>
            <version>${slf4j-api.version}</version>
            <scope>test</scope>
        </dependency>


        <!-- ********************** net (default=netty) ********************** -->

        <!-- netty -->
        <dependency>
            <groupId>io.netty</groupId>
            <artifactId>netty-all</artifactId>
            <version>${netty.version}</version>
        </dependency>

        <!-- mina -->
        <dependency>
            <groupId>org.apache.mina</groupId>
            <artifactId>mina-core</artifactId>
            <version>${mina.version}</version>
            <scope>provided</scope>
        </dependency>

        <!-- jetty (javax.servlet-api) -->
        <dependency>
            <groupId>org.eclipse.jetty</groupId>
            <artifactId>jetty-server</artifactId>
            <version>${jetty-server.version}</version>
            <scope>provided</scope>
        </dependency>
        <dependency>
            <groupId>org.eclipse.jetty</groupId>
            <artifactId>jetty-client</artifactId>
            <version>${jetty-server.version}</version>
            <scope>provided</scope>
        </dependency>


        <!-- ********************** serialize (default=hessian) ********************** -->

        <!-- hessian -->
        <dependency>
            <groupId>com.caucho</groupId>
            <artifactId>hessian</artifactId>
            <version>${hessian.version}</version>
        </dependency>

        <!-- protostuff + objenesis (provided) -->
        <dependency>
            <groupId>io.protostuff</groupId>
            <artifactId>protostuff-core</artifactId>
            <version>${protostuff.version}</version>
            <scope>provided</scope>
        </dependency>
        <dependency>
            <groupId>io.protostuff</groupId>
            <artifactId>protostuff-runtime</artifactId>
            <version>${protostuff.version}</version>
            <scope>provided</scope>
        </dependency>
        <dependency>
            <groupId>org.objenesis</groupId>
            <artifactId>objenesis</artifactId>
            <version>${objenesis.version}</version>
            <scope>provided</scope>
        </dependency>

        <!-- kryo (provided) -->
        <dependency>
            <groupId>com.esotericsoftware</groupId>
            <artifactId>kryo</artifactId>
            <version>${kryo.version}</version>
            <scope>provided</scope>
        </dependency>

        <!-- jackson (provided) -->
        <dependency>
            <groupId>com.fasterxml.jackson.core</groupId>
            <artifactId>jackson-databind</artifactId>
            <version>${jackson.version}</version>
            <scope>provided</scope>
        </dependency>


        <!-- ********************** registry  (default=xxl-registry) ********************** -->

        <!-- xxl-registry-client -->
        <dependency>
            <groupId>com.xuxueli</groupId>
            <artifactId>xxl-registry-client</artifactId>
            <version>${xxl-registry.version}</version>
        </dependency>

        <!-- zookeeper (provided) -->
        <dependency>
            <groupId>org.apache.zookeeper</groupId>
            <artifactId>zookeeper</artifactId>
            <version>${zookeeper.version}</version>
            <scope>provided</scope>
        </dependency>


        <!-- ********************** spring (default=provided) ********************** -->

        <!-- spring -->
        <dependency>
            <groupId>org.springframework</groupId>
            <artifactId>spring-beans</artifactId>
            <version>${spring.version}</version>
            <scope>provided</scope>
            <exclusions>
                <exclusion>
                    <groupId>*</groupId>
                    <artifactId>*</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>org.springframework</groupId>
            <artifactId>spring-core</artifactId>
            <version>${spring.version}</version>
            <scope>provided</scope>
            <exclusions>
                <exclusion>
                    <groupId>*</groupId>
                    <artifactId>*</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>org.springframework</groupId>
            <artifactId>spring-context</artifactId>
            <version>${spring.version}</version>
            <scope>provided</scope>
            <exclusions>
                <exclusion>
                    <groupId>*</groupId>
                    <artifactId>*</artifactId>
                </exclusion>
            </exclusions>
        </dependency>

        <!-- groovy-all -->
        <dependency>
            <groupId>org.codehaus.groovy</groupId>
            <artifactId>groovy</artifactId>
            <version>${groovy.version}</version>
        </dependency>

        <!-- commons-exec -->
        <dependency>
            <groupId>org.apache.commons</groupId>
            <artifactId>commons-exec</artifactId>
            <version>${commons-exec.version}</version>
        </dependency>

    </dependencies>

    <build>
        <plugins>
            <plugin>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-maven-plugin</artifactId>
            </plugin>
        </plugins>
    </build>

    <!--<build>-->
    <!--    <finalName>app</finalName>-->
    <!--    <plugins>-->
    <!--        <plugin>-->
    <!--            <groupId>org.springframework.boot</groupId>-->
    <!--            <artifactId>spring-boot-maven-plugin</artifactId>-->
    <!--        </plugin>-->
    <!--        &lt;!&ndash; docker的maven插件，官网 https://github.com/spotify/docker-maven-plugin &ndash;&gt;-->
    <!--        <plugin>-->
    <!--            <groupId>com.spotify</groupId>-->
    <!--            <artifactId>docker-maven-plugin</artifactId>-->
    <!--            <version>0.4.13</version>-->
    <!--            <configuration>-->
    <!--                <imageName>***********:5000/eos/${project.artifactId}:${project.version}</imageName>-->
    <!--                &lt;!&ndash;docker pull hub.eos.h3c.com/base/openjdk:11-jre&ndash;&gt;-->
    <!--                <baseImage>hub.eos.h3c.com/base/openjdk:11-jre</baseImage>-->
    <!--                <entryPoint>["java", "-jar","/${project.build.finalName}.jar"]</entryPoint>-->
    <!--                <skipDockerBuild>false</skipDockerBuild>-->
    <!--                <resources>-->
    <!--                    <resource>-->
    <!--                        <targetPath>/</targetPath>-->
    <!--                        <directory>${project.build.directory}</directory>-->
    <!--                        <include>${project.build.finalName}.jar</include>-->
    <!--                    </resource>-->
    <!--                </resources>-->
    <!--                <dockerHost>http://***********:2375</dockerHost>-->
    <!--            </configuration>-->
    <!--            <dependencies>-->
    <!--                <dependency>-->
    <!--                    <groupId>javax.activation</groupId>-->
    <!--                    <artifactId>activation</artifactId>-->
    <!--                    <version>1.1.1</version>-->
    <!--                </dependency>-->
    <!--            </dependencies>-->
    <!--        </plugin>-->
    <!--    </plugins>-->
    <!--</build>-->

    <!--<repositories>-->
    <!--    <repository>-->
    <!--        <id>central</id>-->
    <!--        <name>Central Repository</name>-->
    <!--        <url>http://mirrors.h3c.com:8080/repository/maven-public/</url>-->
    <!--        <layout>default</layout>-->
    <!--        <snapshots>-->
    <!--            <enabled>false</enabled>-->
    <!--        </snapshots>-->
    <!--    </repository>-->
    <!--</repositories>-->


</project>
