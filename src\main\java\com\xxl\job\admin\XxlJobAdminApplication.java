package com.xxl.job.admin;

import com.zaxxer.hikari.HikariDataSource;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.cloud.openfeign.EnableFeignClients;

import javax.annotation.PostConstruct;
import javax.sql.DataSource;

/**
 * <AUTHOR> 2018-10-28 00:38:13
 */
@SpringBootApplication
@EnableFeignClients
public class XxlJobAdminApplication {
	public static void main(String[] args) {
        SpringApplication.run(XxlJobAdminApplication.class, args);
	}

	@Autowired
	private DataSource dataSource;

	@PostConstruct
	public void printHikariCPMaxConnections() {
		if (dataSource instanceof HikariDataSource) {
			HikariDataSource hikariDataSource = (HikariDataSource) dataSource;
			int maxPoolSize = hikariDataSource.getMaximumPoolSize();
			System.out.println("============== HikariCP 最大连接数: " + maxPoolSize);
		} else {
			System.out.println("============== 数据源不是 HikariCP 类型");
		}
	}

}