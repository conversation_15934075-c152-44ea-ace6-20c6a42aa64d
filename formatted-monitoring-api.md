# 格式化监控API使用指南

## 新增的监控接口

### 1. 原始状态接口（保持兼容）
```bash
curl http://localhost:8080/api/connection-pool/status
```

**响应示例**：
```json
{
  "success": true,
  "connectClientPool": "Server[lys3415-02/************, PID:6036] - ConnectClient Pool: total=1, valid=1, expired=0\\n=== Connection Details ===\\n• ************:9999 -> VALID (age: 4 min, created: 14:23:15)",
  "timestamp": 1755759639635,
  "readableTime": "2025-01-21 14:27:19"
}
```

### 2. 格式化状态接口（推荐使用）
```bash
curl http://localhost:8080/api/connection-pool/status/formatted
```

**响应示例**：
```json
{
  "success": true,
  "summary": "Server[lys3415-02/************, PID:6036] - ConnectClient Pool: total=1, valid=1, expired=0",
  "connections": [
    "=== Connection Details ===",
    "• ************:9999 -> VALID (age: 4 min, created: 14:23:15)"
  ],
  "timestamp": 1755759639635,
  "readableTime": "2025-01-21 14:27:19"
}
```

## 数据格式说明

### 服务器信息格式
```
Server[hostname/IP, PID:进程ID]
```
- **hostname**: 服务器主机名
- **IP**: 服务器IP地址  
- **PID**: Java进程ID

### 连接状态格式
```
• 地址 -> 状态 (age: 年龄 min, created: 创建时间)
```
- **地址**: 客户端地址
- **状态**: VALID/EXPIRED/INVALID
- **年龄**: 连接存活时间（分钟）
- **创建时间**: 连接创建的具体时间（HH:mm:ss）

## 多实例监控示例

### 实例1监控结果
```json
{
  "summary": "Server[xxl-job-01/************, PID:12345] - ConnectClient Pool: total=3, valid=2, expired=1",
  "connections": [
    "=== Connection Details ===",
    "• ************0:9999 -> VALID (age: 2 min, created: 14:25:30)",
    "• ************1:9999 -> VALID (age: 5 min, created: 14:22:15)", 
    "• ************2:9999 -> EXPIRED (age: 8 min, created: 14:19:45)"
  ]
}
```

### 实例2监控结果
```json
{
  "summary": "Server[xxl-job-02/************, PID:23456] - ConnectClient Pool: total=2, valid=2, expired=0",
  "connections": [
    "=== Connection Details ===",
    "• ************0:9999 -> VALID (age: 3 min, created: 14:24:20)",
    "• ************1:9999 -> VALID (age: 6 min, created: 14:21:30)"
  ]
}
```

## 监控脚本优化

### 格式化监控脚本
```bash
#!/bin/bash
# formatted-monitor.sh

echo "=== ConnectClient多实例格式化监控 ==="

while true; do
    echo "=== $(date) ==="
    
    # 调用格式化接口
    response=$(curl -s http://localhost:8080/api/connection-pool/status/formatted)
    
    # 解析并显示
    echo "$response" | jq -r '
        if .success then
            "服务器: " + .summary + "\n" +
            "时间: " + .readableTime + "\n" +
            "连接详情:" + 
            (if .connections then 
                (.connections | map("  " + .) | join("\n"))
            else 
                "  无连接"
            end)
        else
            "错误: " + .error
        end
    '
    
    echo "================================"
    sleep 30
done
```

### 多实例对比脚本
```bash
#!/bin/bash
# compare-instances.sh

echo "=== 多实例连接池对比 ==="

# 实例列表
instances=(
    "http://************:8080"
    "http://************:8080" 
    "http://************:8080"
)

for instance in "${instances[@]}"; do
    echo "--- $instance ---"
    
    response=$(curl -s "$instance/api/connection-pool/status/formatted")
    
    echo "$response" | jq -r '
        if .success then
            .summary + "\n连接数: " + (.connections | length | tostring) + "\n"
        else
            "无法连接或出错: " + (.error // "未知错误")
        end
    '
done
```

## 优势对比

### 原始接口的问题
- JSON中的`\n`不会自动换行
- 数据混在一个字符串中，难以解析
- 不便于程序化处理

### 格式化接口的优势
- **结构化数据**: summary和connections分离
- **易于解析**: 可以单独处理每个连接信息
- **程序友好**: 便于脚本和程序处理
- **可读性强**: 添加了可读时间格式

## 使用建议

### 人工查看
使用格式化接口 + jq 处理：
```bash
curl -s http://localhost:8080/api/connection-pool/status/formatted | jq -r '.summary, .connections[]'
```

### 程序监控
使用格式化接口解析JSON：
```javascript
fetch('/api/connection-pool/status/formatted')
  .then(response => response.json())
  .then(data => {
    console.log('服务器:', data.summary);
    data.connections?.forEach(conn => console.log(conn));
  });
```

### 日志记录
两个接口都会记录相同的日志信息，可以结合使用。

现在你可以使用格式化接口获得更好的监控体验！
