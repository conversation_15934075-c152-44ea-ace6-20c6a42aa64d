package com.xxl.job.admin.controller;

import com.xxl.job.admin.controller.annotation.PermissionLimit;
import com.xxl.rpc.remoting.net.common.ConnectClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 连接池监控控制器
 * 提供连接池状态查询接口
 * 
 * <AUTHOR>
 */
@RestController
@RequestMapping("/api/connection-pool")
public class ConnectionPoolController {
    
    /**
     * 获取ConnectClient连接池状态
     */
    @GetMapping("/status")
    @PermissionLimit(limit = false)
    public Map<String, Object> getConnectionPoolStatus() {
        Map<String, Object> result = new HashMap<>();

        try {
            String connectClientStatus = ConnectClient.getConnectionPoolStatus();

            result.put("success", true);
            result.put("connectClientPool", connectClientStatus);
            result.put("timestamp", System.currentTimeMillis());
            result.put("readableTime", new java.text.SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(new java.util.Date()));
        } catch (Exception e) {
            result.put("success", false);
            result.put("error", e.getMessage());
            result.put("timestamp", System.currentTimeMillis());
            result.put("readableTime", new java.text.SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(new java.util.Date()));
        }

        return result;
    }
    
    /**
     * 获取连接池健康检查
     */
    @GetMapping("/health")
    @PermissionLimit(limit = false)
    public Map<String, Object> getConnectionPoolHealth() {
        Map<String, Object> result = new HashMap<>();

        try {
            String connectClientStatus = ConnectClient.getConnectionPoolStatus();
            boolean healthy = !connectClientStatus.contains("Error");

            result.put("healthy", healthy);
            result.put("connectClientPool", connectClientStatus);
            result.put("timestamp", System.currentTimeMillis());
        } catch (Exception e) {
            result.put("healthy", false);
            result.put("error", e.getMessage());
            result.put("timestamp", System.currentTimeMillis());
        }

        return result;
    }

    /**
     * 获取格式化的连接池状态（便于查看）
     */
    @GetMapping("/status/formatted")
    @PermissionLimit(limit = false)
    public Map<String, Object> getFormattedConnectionPoolStatus() {
        Map<String, Object> result = new HashMap<>();

        try {
            String rawStatus = ConnectClient.getConnectionPoolStatus();

            // 解析状态信息
            String[] lines = rawStatus.split("\\\\n");
            String summary = lines.length > 0 ? lines[0] : rawStatus;

            result.put("success", true);
            result.put("summary", summary);

            if (lines.length > 1) {
                List<String> details = new ArrayList<>();
                for (int i = 1; i < lines.length; i++) {
                    if (!lines[i].trim().isEmpty()) {
                        details.add(lines[i]);
                    }
                }
                result.put("connections", details);
            }

            result.put("timestamp", System.currentTimeMillis());
            result.put("readableTime", new java.text.SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(new java.util.Date()));

        } catch (Exception e) {
            result.put("success", false);
            result.put("error", e.getMessage());
            result.put("timestamp", System.currentTimeMillis());
            result.put("readableTime", new java.text.SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(new java.util.Date()));
        }

        return result;
    }
}
