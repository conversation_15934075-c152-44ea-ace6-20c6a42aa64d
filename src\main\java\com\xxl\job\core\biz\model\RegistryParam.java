package com.xxl.job.core.biz.model;

import java.io.Serializable;

/**
 * Created by x<PERSON><PERSON><PERSON> on 2017-05-10 20:22:42
 */
public class RegistryParam implements Serializable {
    private static final long serialVersionUID = 42L;

    private String registGroup;
    private String registryKey;
    private String registryValue;
    private String registryParams;

    public RegistryParam(){}
    public RegistryParam(String registGroup, String registryKey, String registryValue, String registryParams) {
        this.registGroup = registGroup;
        this.registryKey = registryKey;
        this.registryValue = registryValue;
        this.registryParams = registryParams;
    }

    public String getRegistGroup() {
        return registGroup;
    }

    public void setRegistGroup(String registGroup) {
        this.registGroup = registGroup;
    }

    public String getRegistryKey() {
        return registryKey;
    }

    public void setRegistryKey(String registryKey) {
        this.registryKey = registryKey;
    }

    public String getRegistryValue() {
        return registryValue;
    }

    public void setRegistryValue(String registryValue) {
        this.registryValue = registryValue;
    }

    public String getRegistryParams() {
        return registryParams;
    }

    public void setRegistryParams(String registryParams) {
        this.registryParams = registryParams;
    }

    @Override
    public String toString() {
        return "RegistryParam{" +
                "registGroup='" + registGroup + '\'' +
                ", registryKey='" + registryKey + '\'' +
                ", registryValue='" + registryValue + '\'' +
                ", registryParams='" + registryParams + '\'' +
                '}';
    }
}
