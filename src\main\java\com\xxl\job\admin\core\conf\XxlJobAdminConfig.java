package com.xxl.job.admin.core.conf;

import com.xxl.job.admin.dao.XxlJobGroupDao;
import com.xxl.job.admin.dao.XxlJobInfoDao;
import com.xxl.job.admin.dao.XxlJobLogDao;
import com.xxl.job.admin.dao.XxlJobRegistryDao;
import com.xxl.job.core.biz.AdminBiz;
import org.springframework.beans.factory.InitializingBean;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.mail.javamail.JavaMailSender;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import javax.sql.DataSource;
import java.util.List;

/**
 * xxl-job config
 *
 * <AUTHOR> 2017-04-28
 */
@Component
public class XxlJobAdminConfig implements InitializingBean{
    private static XxlJobAdminConfig adminConfig = null;
    public static XxlJobAdminConfig getAdminConfig() {
        return adminConfig;
    }

    @Override
    public void afterPropertiesSet() throws Exception {
        adminConfig = this;
    }

    // conf
    @Value("${xxl.job.i18n}")
    private String i18n;

    @Value("${xxl.job.accessToken}")
    private String accessToken;

    @Value("${spring.mail.username}")
    private String emailUserName;

    @Value("${feiyun.ip.subnet}")
    private List<String> feiYunIpSubnet;

    @Value("${pod.ip.subnet}")
    private String proIpSubnet;

    @Value("${eos.ip.subnet}")
    private String eosIpSubnet;

    @Value("${subnet.enable}")
    private boolean subnetEnable;

    @Value("${ip.whitelist}")
    private String ipWhitelist;

    @Value("${ip.whitelist.enable}")
    private boolean ipWhitelistEnable;

    // dao, service

    @Resource
    private XxlJobLogDao xxlJobLogDao;
    @Resource
    private XxlJobInfoDao xxlJobInfoDao;
    @Resource
    private XxlJobRegistryDao xxlJobRegistryDao;
    @Resource
    private XxlJobGroupDao xxlJobGroupDao;
    @Resource
    private AdminBiz adminBiz;
    @Resource
    private JavaMailSender mailSender;
    @Resource
    private DataSource dataSource;


    public String getI18n() {
        return i18n;
    }

    public String getAccessToken() {
        return accessToken;
    }

    public String getEmailUserName() {
        return emailUserName;
    }

    public XxlJobLogDao getXxlJobLogDao() {
        return xxlJobLogDao;
    }

    public XxlJobInfoDao getXxlJobInfoDao() {
        return xxlJobInfoDao;
    }

    public XxlJobRegistryDao getXxlJobRegistryDao() {
        return xxlJobRegistryDao;
    }

    public XxlJobGroupDao getXxlJobGroupDao() {
        return xxlJobGroupDao;
    }

    public AdminBiz getAdminBiz() {
        return adminBiz;
    }

    public JavaMailSender getMailSender() {
        return mailSender;
    }

    public DataSource getDataSource() {
        return dataSource;
    }

    public List<String> getFeiYunIpSubnet() {
        return feiYunIpSubnet;
    }

    public String getProIpSubnet() {
        return proIpSubnet;
    }

    public String getEosIpSubnet() {
        return eosIpSubnet;
    }

    public boolean isSubnetEnable() {
        return subnetEnable;
    }

    public String getIpWhitelist() {
        return ipWhitelist;
    }

    public boolean getIpWhitelistEnable() {
        return ipWhitelistEnable;
    }
}
