//package com.xxl.job.admin.service;
//
//import com.eos.common.POJO.DTO.PropertyDTO;
//import com.eos.common.service.BaseHealthy;
//import org.springframework.stereotype.Service;
//
//@Service
//public class HealthyService implements BaseHealthy {
//    PropertyDTO propertyDTO = new PropertyDTO();
//
//    @Override
//    public PropertyDTO findByHostName(String hostName){
//        return propertyDTO;
//    }
//    @Override
//    public void save(PropertyDTO property){
//        this.propertyDTO.setHostName(property.getHostName());
//        this.propertyDTO.setHostAddress(property.getHostAddress());
//        this.propertyDTO.setStatus(property.getStatus());
//        this.propertyDTO.setOsName(property.getOsName());
//        this.propertyDTO.setUpdateTime(property.getUpdateTime());
//        this.propertyDTO.setCreateTime(property.getCreateTime());
//    }
//}
