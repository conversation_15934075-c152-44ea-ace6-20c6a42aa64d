package com.xxl.job.admin.client;

import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestHeader;
import org.springframework.web.bind.annotation.RequestParam;

/**
 * <AUTHOR>
 * @date 2020/6/29 14:51
 */
@FeignClient(name = "${eos.project.name}", url = "${eos.project.url}")
// @FeignClient(name = "${eos.project.name}", url = "http://127.0.0.1:8020")
public interface ServerHttpClient {
    @GetMapping(value = "service", consumes = {"application/json"}, headers = {"Authorization={Authorization}"})
    String getServer(@RequestParam(name = "project") String projectId, @RequestParam("Authorization") String authorization, @RequestHeader("projectId") String project);
}
