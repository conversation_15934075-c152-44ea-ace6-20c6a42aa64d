package com.xxl.rpc.remoting.net.common;

import com.xxl.rpc.remoting.invoker.XxlRpcInvokerFactory;
import com.xxl.rpc.remoting.invoker.reference.XxlRpcReferenceBean;
import com.xxl.rpc.remoting.net.impl.netty.client.NettyConnectClient;
import com.xxl.rpc.remoting.net.params.XxlRpcRequest;
import com.xxl.rpc.serialize.impl.HessianSerializer;
import org.junit.Test;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.UUID;
import java.util.concurrent.TimeUnit;

/**
 * 测试连接续期功能
 * 验证发送数据时是否能正确续期7分钟
 */
public class ConnectClientRenewalTest {
    private static final Logger logger = LoggerFactory.getLogger(ConnectClientRenewalTest.class);

    @Test
    public void testConnectionRenewal() throws Exception {
        logger.info("=== 开始测试连接续期功能 ===");

        // 创建测试用的ReferenceBean
        XxlRpcReferenceBean referenceBean = createTestReferenceBean();
        String testAddress = "127.0.0.1:9999"; // 测试地址

        try {
            // 1. 模拟第一次发送 - 创建连接
            logger.info("1. 第一次发送，创建新连接...");
            XxlRpcRequest request1 = createTestRequest();
            
            try {
                ConnectClient.asyncSend(request1, testAddress, NettyConnectClient.class, referenceBean);
                logger.info("第一次发送成功（预期会失败，因为没有服务端）");
            } catch (Exception e) {
                logger.info("第一次发送失败（预期行为）: {}", e.getMessage());
            }

            // 2. 显示连接状态
            logger.info("2. 显示当前连接状态:");
            String status1 = ConnectClient.getConnectionStatus();
            logger.info("连接状态:\n{}", status1);

            // 3. 等待3秒后再次发送 - 测试续期
            logger.info("3. 等待3秒后再次发送，测试续期功能...");
            Thread.sleep(3000);

            XxlRpcRequest request2 = createTestRequest();
            try {
                ConnectClient.asyncSend(request2, testAddress, NettyConnectClient.class, referenceBean);
                logger.info("第二次发送成功（预期会失败，但应该续期了）");
            } catch (Exception e) {
                logger.info("第二次发送失败（预期行为）: {}", e.getMessage());
            }

            // 4. 再次显示连接状态，验证续期效果
            logger.info("4. 显示续期后的连接状态:");
            String status2 = ConnectClient.getConnectionStatus();
            logger.info("续期后连接状态:\n{}", status2);

            // 5. 等待一段时间，验证连接是否按预期保持活跃
            logger.info("5. 等待5秒，验证连接保持活跃...");
            Thread.sleep(5000);

            String status3 = ConnectClient.getConnectionStatus();
            logger.info("5秒后连接状态:\n{}", status3);

            logger.info("=== 连接续期功能测试完成 ===");

        } catch (Exception e) {
            logger.error("测试过程中发生异常", e);
            throw e;
        }
    }

    /**
     * 创建测试用的ReferenceBean
     */
    private XxlRpcReferenceBean createTestReferenceBean() {
        XxlRpcReferenceBean referenceBean = new XxlRpcReferenceBean();
        
        // 创建InvokerFactory
        XxlRpcInvokerFactory invokerFactory = new XxlRpcInvokerFactory();
        try {
            invokerFactory.start();
        } catch (Exception e) {
            logger.warn("启动InvokerFactory失败", e);
        }
        
        referenceBean.setInvokerFactory(invokerFactory);
        referenceBean.setSerializer(new HessianSerializer());
        referenceBean.setTimeout(5000);
        
        return referenceBean;
    }

    /**
     * 创建测试用的RPC请求
     */
    private XxlRpcRequest createTestRequest() {
        XxlRpcRequest request = new XxlRpcRequest();
        request.setRequestId(UUID.randomUUID().toString());
        request.setCreateMillisTime(System.currentTimeMillis());
        request.setClassName("TestService");
        request.setMethodName("testMethod");
        request.setParameterTypes(new Class[]{String.class});
        request.setParameters(new Object[]{"test"});
        return request;
    }

    /**
     * 测试连接过期清理功能
     */
    @Test
    public void testConnectionExpiration() throws Exception {
        logger.info("=== 开始测试连接过期清理功能 ===");

        XxlRpcReferenceBean referenceBean = createTestReferenceBean();
        String testAddress = "127.0.0.1:8888";

        try {
            // 1. 创建连接
            logger.info("1. 创建测试连接...");
            XxlRpcRequest request = createTestRequest();
            
            try {
                ConnectClient.asyncSend(request, testAddress, NettyConnectClient.class, referenceBean);
            } catch (Exception e) {
                logger.info("发送失败（预期行为）: {}", e.getMessage());
            }

            // 2. 显示初始状态
            String status1 = ConnectClient.getConnectionStatus();
            logger.info("初始连接状态:\n{}", status1);

            // 3. 等待一段时间不发送数据，让连接变为不活跃
            logger.info("2. 等待10秒，模拟连接不活跃状态...");
            Thread.sleep(10000);

            // 4. 显示不活跃状态
            String status2 = ConnectClient.getConnectionStatus();
            logger.info("10秒后连接状态:\n{}", status2);

            // 5. 手动触发清理
            logger.info("3. 手动触发连接清理...");
            ConnectClient.manualCleanupExpiredConnections();

            // 6. 显示清理后状态
            String status3 = ConnectClient.getConnectionStatus();
            logger.info("清理后连接状态:\n{}", status3);

            logger.info("=== 连接过期清理功能测试完成 ===");

        } catch (Exception e) {
            logger.error("测试过程中发生异常", e);
            throw e;
        }
    }
}
