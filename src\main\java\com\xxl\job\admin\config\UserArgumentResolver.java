package com.xxl.job.admin.config;


import com.eos.common.constant.StatusCodeEnum;
import com.eos.common.custom.BusinessException;
import com.eos.common.domain.ActiveUser;
import com.eos.common.util.UserUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.MethodParameter;
import org.springframework.stereotype.Component;
import org.springframework.web.bind.support.WebDataBinderFactory;
import org.springframework.web.context.request.NativeWebRequest;
import org.springframework.web.method.support.HandlerMethodArgumentResolver;
import org.springframework.web.method.support.ModelAndViewContainer;

/**
 * 获取用户的session dto
 */
@Component
public class UserArgumentResolver implements HandlerMethodArgumentResolver {

	@Autowired
	UserUtils userUtils;

	@Override
	public Object resolveArgument(MethodParameter methodParameter, ModelAndViewContainer arg1,
                                  NativeWebRequest webRequest, WebDataBinderFactory binderFactory) throws Exception {
		ActiveUser activeUser = userUtils.getActiveUser();
		if(activeUser != null){
			return activeUser;
		}else {
			throw new BusinessException(true, StatusCodeEnum.USER_NOT_EXIST);
		}
	}

	@Override
	public boolean supportsParameter(MethodParameter arg0) {
		return arg0.getParameterType().equals(ActiveUser.class);
	}

}