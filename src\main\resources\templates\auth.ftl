<!DOCTYPE html>
<html lang="en">
<body>
<div>
    <!-- header -->
    <script src="${request.contextPath}/static/adminlte/bower_components/jquery/jquery.min.js"></script>
    <form id="loginForm" method="post" style="display: none">
        <div class="login-box-body">
            <p class="login-box-msg"></p>
            <div class="form-group has-feedback">
                <input type="text" name="userName" class="form-control" maxlength="18" value="${eosJobAuth.userName}">
                <span class="glyphicon glyphicon-envelope form-control-feedback"></span>
            </div>
            <div class="form-group has-feedback">
                <input type="password" name="password" class="form-control" maxlength="18" value="${eosJobAuth.password}">
                <span class="glyphicon glyphicon-lock form-control-feedback"></span>
            </div>
        </div>
    </form>
</div>
<script>
    $(function () {
        var base_url = '${request.contextPath}';
        var request_url = '${eosJobAuth.url}';
        var access = '${eosJobAuth.access}';
        var projectId = '${eosJobAuth.projectId}';
        var requestType = '${requestType}';
        $.post(base_url + "/login?type="+requestType, $("#loginForm").serialize(), function (data, status) {
            if (data.code === 200) {
                setTimeout(function () {
                    // console.log(base_url + request_url)
                    window.location.href = base_url + '/' + request_url + "?projectId=" + projectId + "&access=" + access;
                }, 300);
            } else {
                // layer.open({
                //     title: I18n.system_tips,
                //     btn: [ I18n.system_ok ],
                //     content: (data.msg || I18n.login_fail ),
                //     icon: '2'
                // });
            }
        });
    });
</script>

</body>
</html>
