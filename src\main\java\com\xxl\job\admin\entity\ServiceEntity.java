package com.xxl.job.admin.entity;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;

/**
 * <AUTHOR>
 * @date 2020/6/30 9:53
 */
public class ServiceEntity {
    private String id;
    private String name;
    private String configId;
    private String sonarKey;
    private String project;
    private String password;
    private String host;
    private String api;

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getConfigId() {
        return configId;
    }

    public void setConfigId(String configId) {
        this.configId = configId;
    }

    public String getSonarKey() {
        return sonarKey;
    }

    public void setSonarKey(String sonarKey) {
        this.sonarKey = sonarKey;
    }

    public String getProject() {
        return project;
    }

    public void setProject(String project) {
        this.project = project;
    }

    public String getPassword() {
        return password;
    }

    public void setPassword(String password) {
        this.password = password;
    }

    public String getHost() {
        return host;
    }

    public void setHost(String host) {
        this.host = host;
    }

    public String getApi() {
        return api;
    }

    public void setApi(String api) {
        this.api = api;
    }

    public String getControllor() {
        return controllor;
    }

    public void setControllor(String controllor) {
        this.controllor = controllor;
    }

    public String getCpu() {
        return cpu;
    }

    public void setCpu(String cpu) {
        this.cpu = cpu;
    }

    public String getMemory() {
        return memory;
    }

    public void setMemory(String memory) {
        this.memory = memory;
    }

    public String getVolume() {
        return volume;
    }

    public void setVolume(String volume) {
        this.volume = volume;
    }

    public String getNode() {
        return node;
    }

    public void setNode(String node) {
        this.node = node;
    }

    public Integer getReplicas() {
        return replicas;
    }

    public void setReplicas(Integer replicas) {
        this.replicas = replicas;
    }

    public JSONArray getApiRoute() {
        return apiRoute;
    }

    public void setApiRoute(JSONArray apiRoute) {
        this.apiRoute = apiRoute;
    }

    public JSONObject getIpAndPort() {
        return ipAndPort;
    }

    public void setIpAndPort(JSONObject ipAndPort) {
        this.ipAndPort = ipAndPort;
    }

    public String getDefender() {
        return defender;
    }

    public void setDefender(String defender) {
        this.defender = defender;
    }

    public Integer getStatus() {
        return status;
    }

    public void setStatus(Integer status) {
        this.status = status;
    }

    public String getDesci() {
        return desci;
    }

    public void setDesci(String desci) {
        this.desci = desci;
    }

    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = remark;
    }

    public Integer getUseCount() {
        return useCount;
    }

    public void setUseCount(Integer useCount) {
        this.useCount = useCount;
    }

    public String getCreateTime() {
        return createTime;
    }

    public void setCreateTime(String createTime) {
        this.createTime = createTime;
    }

    public String getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(String updateTime) {
        this.updateTime = updateTime;
    }

    public Integer getWorkStatus() {
        return workStatus;
    }

    public void setWorkStatus(Integer workStatus) {
        this.workStatus = workStatus;
    }

    public String getImageVersion() {
        return imageVersion;
    }

    public void setImageVersion(String imageVersion) {
        this.imageVersion = imageVersion;
    }

    public String getBpmId() {
        return bpmId;
    }

    public void setBpmId(String bpmId) {
        this.bpmId = bpmId;
    }

    private String controllor;
    /**
     * cpu（不能超过所属应用的总cpu）
     */
    private String cpu;
    /**
     * memory（不能超过所属应用的总内存）
     */
    private String memory;
    /**
     * 存储空间（不能超过所属应用的总存储空间）
     */
    private String volume;
    /**
     * 区域
     */
    private String node;
    /**
     * 副本数量
     */
    private Integer replicas;
    private JSONArray apiRoute;
    private JSONObject ipAndPort;
    private String defender;
    private Integer status;
    private String desci;
    private String remark;
    private Integer useCount;
    private String createTime;
    private String updateTime;
    /**
     * 工作状态 1=未上线 2=试运行 3=已转产
     */
    private Integer workStatus;
    /**
     * 最新镜像版本
     */
    private String imageVersion;
    private String bpmId;
}
